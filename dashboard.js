// Dashboard functionality for IsotopeAI - YT Tracker

class Dashboard {
    constructor() {
        this.playlists = [];
        this.allVideos = [];
        this.analytics = {
            totalPlaylists: 0,
            totalVideos: 0,
            completedVideos: 0,
            inProgressVideos: 0,
            totalWatchTime: 0,
            currentStreak: 0,
            weeklyActivity: []
        };
        
        this.init();
    }

    async init() {
        console.log('Initializing dashboard...');
        await this.loadData();
        this.bindEvents();
        this.renderDashboard();
    }

    async loadData() {
        try {
            // Use the same storage manager as main app
            this.storage = window.storageManager;
            
            // Load all playlists from storage
            this.playlists = this.storage.getAllPlaylists();
            
            // No sample data - users must add real playlists

            this.calculateAnalytics();
            console.log('Data loaded:', this.playlists);
        } catch (error) {
            console.error('Error loading data:', error);
            this.playlists = [];
            this.calculateAnalytics();
        }
    }



    calculateAnalytics() {
        // Use real analytics from StorageManager
        const realAnalytics = this.storage.calculateRealAnalytics();

        this.analytics = {
            ...this.analytics,
            ...realAnalytics
        };

        // Rebuild allVideos array for UI rendering
        this.allVideos = [];
        this.playlists.forEach(playlist => {
            if (playlist.videos) {
                this.allVideos.push(...playlist.videos.map(video => ({
                    ...video,
                    playlistTitle: playlist.title,
                    playlistId: playlist.id
                })));
            }
        });
    }

    bindEvents() {
        // Add playlist button
        const addPlaylistBtn = document.getElementById('addPlaylistBtn');
        if (addPlaylistBtn) {
            addPlaylistBtn.addEventListener('click', () => this.openAddPlaylistModal());
        }

        // Modal events
        const closeAddPlaylistBtn = document.getElementById('closeAddPlaylistBtn');
        const cancelAddPlaylistBtn = document.getElementById('cancelAddPlaylistBtn');
        const confirmAddPlaylistBtn = document.getElementById('confirmAddPlaylistBtn');

        if (closeAddPlaylistBtn) {
            closeAddPlaylistBtn.addEventListener('click', () => this.closeAddPlaylistModal());
        }
        if (cancelAddPlaylistBtn) {
            cancelAddPlaylistBtn.addEventListener('click', () => this.closeAddPlaylistModal());
        }
        if (confirmAddPlaylistBtn) {
            confirmAddPlaylistBtn.addEventListener('click', () => this.addNewPlaylist());
        }

        // Filter buttons
        const filterBtns = document.querySelectorAll('.filter-btn');
        filterBtns.forEach(btn => {
            btn.addEventListener('click', (e) => this.handleFilterChange(e));
        });

    }

    renderDashboard() {
        this.renderOverviewStats();
        this.renderPlaylists();
        this.renderVideos();
        this.renderProgressChart();
        this.renderWeeklyActivity();
        this.renderStreak();
    }

    renderOverviewStats() {
        const totalPlaylistsEl = document.getElementById('totalPlaylists');
        const totalVideosEl = document.getElementById('totalVideos');
        const completedVideosEl = document.getElementById('completedVideos');
        const totalWatchTimeEl = document.getElementById('totalWatchTime');

        if (totalPlaylistsEl) totalPlaylistsEl.textContent = this.analytics.totalPlaylists;
        if (totalVideosEl) totalVideosEl.textContent = this.analytics.totalVideos;
        if (completedVideosEl) completedVideosEl.textContent = this.analytics.completedVideos;
        if (totalWatchTimeEl) {
            const hours = Math.floor(this.analytics.totalWatchTime / 3600);
            const minutes = Math.floor((this.analytics.totalWatchTime % 3600) / 60);
            totalWatchTimeEl.textContent = `${hours}h ${minutes}m`;
        }
    }

    renderPlaylists() {
        const playlistsGrid = document.getElementById('playlistsGrid');
        if (!playlistsGrid) return;

        if (this.playlists.length === 0) {
            playlistsGrid.innerHTML = `
                <div class="empty-state">
                    <div class="empty-state-icon">
                        <i class="fab fa-youtube"></i>
                    </div>
                    <h3>No Playlists Yet</h3>
                    <p>Add your first YouTube playlist to start tracking your learning progress</p>
                    <button class="btn btn--primary" onclick="dashboard.openAddPlaylistModal()">
                        <i class="fas fa-plus"></i> Add Your First Playlist
                    </button>
                </div>
            `;
            return;
        }

        playlistsGrid.innerHTML = this.playlists.map(playlist => {
            const completedCount = playlist.videos ? playlist.videos.filter(v => v.completed).length : 0;
            const totalCount = playlist.videos ? playlist.videos.length : 0;
            const completionPercentage = totalCount > 0 ? Math.round((completedCount / totalCount) * 100) : 0;
            
            return `
                <div class="playlist-card" data-playlist-id="${playlist.id}">
                    <div class="playlist-header">
                        <div class="playlist-thumbnail">
                            <i class="fab fa-youtube"></i>
                        </div>
                        <div class="playlist-info">
                            <h3 class="playlist-title">${playlist.title}</h3>
                            <p class="playlist-channel">${playlist.channel}</p>
                        </div>
                    </div>
                    <div class="playlist-stats">
                        <div class="stat-row">
                            <span class="stat-label">Videos:</span>
                            <span class="stat-value">${totalCount}</span>
                        </div>
                        <div class="stat-row">
                            <span class="stat-label">Completed:</span>
                            <span class="stat-value">${completedCount}/${totalCount}</span>
                        </div>
                        <div class="stat-row">
                            <span class="stat-label">Progress:</span>
                            <span class="stat-value">${completionPercentage}%</span>
                        </div>
                    </div>
                    <div class="playlist-progress">
                        <div class="progress-bar-mini">
                            <div class="progress-fill-mini" style="width: ${completionPercentage}%"></div>
                        </div>
                    </div>
                    <div class="playlist-actions">
                        <button class="btn btn--primary btn--sm" onclick="dashboard.openPlaylist('${playlist.id}')">
                            <i class="fas fa-play"></i> Continue
                        </button>
                        <button class="btn btn--outline btn--sm" onclick="dashboard.viewPlaylistDetails('${playlist.id}')">
                            <i class="fas fa-info"></i> Details
                        </button>
                    </div>
                </div>
            `;
        }).join('');
    }

    renderVideos(filter = 'all') {
        const videosGrid = document.getElementById('videosGrid');
        if (!videosGrid) return;

        let filteredVideos = this.allVideos;
        
        if (filter === 'in-progress') {
            filteredVideos = this.allVideos.filter(v => !v.completed && v.completionPercentage > 0);
        } else if (filter === 'completed') {
            filteredVideos = this.allVideos.filter(v => v.completed);
        }

        // Sort by most recent activity
        filteredVideos.sort((a, b) => {
            if (a.completed && !b.completed) return -1;
            if (!a.completed && b.completed) return 1;
            return b.completionPercentage - a.completionPercentage;
        });

        // Limit to 12 videos for better performance
        filteredVideos = filteredVideos.slice(0, 12);

        videosGrid.innerHTML = filteredVideos.map(video => {
            const statusClass = video.completed ? 'completed' : 
                               video.completionPercentage > 0 ? 'in-progress' : 'unwatched';
            const statusIcon = video.completed ? 'fa-check' : 
                              video.completionPercentage > 0 ? 'fa-play' : 'fa-circle';
            
            return `
                <div class="video-card ${statusClass}" data-video-id="${video.id}">
                    <div class="video-thumbnail-large">
                        <img src="${video.thumbnail}" alt="${video.title}" loading="lazy">
                        <div class="video-overlay">
                            <div class="play-button">
                                <i class="fas ${statusIcon}"></i>
                            </div>
                        </div>
                        <div class="video-progress-overlay">
                            <div class="progress-bar-overlay" style="width: ${video.completionPercentage}%"></div>
                        </div>
                    </div>
                    <div class="video-card-content">
                        <h4 class="video-card-title">${video.title}</h4>
                        <p class="video-card-playlist">${video.playlistTitle}</p>
                        <div class="video-card-meta">
                            <span class="video-duration">${this.formatDuration(video.duration)}</span>
                            <span class="video-progress">${video.completionPercentage}%</span>
                        </div>
                    </div>
                </div>
            `;
        }).join('');
    }

    // Goals & deadlines removed

    renderProgressChart() {
        const completedPercentage = this.analytics.totalVideos > 0 ? 
            Math.round((this.analytics.completedVideos / this.analytics.totalVideos) * 100) : 0;
        const inProgressPercentage = this.analytics.totalVideos > 0 ? 
            Math.round((this.analytics.inProgressVideos / this.analytics.totalVideos) * 100) : 0;
        const unwatchedPercentage = 100 - completedPercentage - inProgressPercentage;

        // Update circular progress
        const progressChart = document.getElementById('overallProgressChart');
        if (progressChart) {
            const circle = progressChart.querySelector('.progress-circle-large');
            const text = progressChart.querySelector('.progress-text-large');
            
            if (circle && text) {
                circle.style.background = `conic-gradient(
                    var(--color-success) 0deg ${completedPercentage * 3.6}deg,
                    var(--color-warning) ${completedPercentage * 3.6}deg ${(completedPercentage + inProgressPercentage) * 3.6}deg,
                    var(--color-secondary) ${(completedPercentage + inProgressPercentage) * 3.6}deg 360deg
                )`;
                text.textContent = `${completedPercentage}%`;
            }
        }

        // Update percentage displays
        const completedEl = document.getElementById('completedPercentage');
        const inProgressEl = document.getElementById('inProgressPercentage');
        const unwatchedEl = document.getElementById('unwatchedPercentage');

        if (completedEl) completedEl.textContent = `${completedPercentage}%`;
        if (inProgressEl) inProgressEl.textContent = `${inProgressPercentage}%`;
        if (unwatchedEl) unwatchedEl.textContent = `${unwatchedPercentage}%`;
    }

    renderWeeklyActivity() {
        const weeklyActivity = document.getElementById('weeklyActivity');
        if (!weeklyActivity) return;

        const maxMinutes = Math.max(...this.analytics.weeklyActivity.map(d => d.minutes));
        // Ensure maxMinutes is at least 1 to avoid division by zero or NaN
        const safeMaxMinutes = maxMinutes > 0 ? maxMinutes : 1;
        
        weeklyActivity.innerHTML = this.analytics.weeklyActivity.map(day => {
            const height = (day.minutes / safeMaxMinutes) * 100;
            return `
                <div class="activity-day">
                    <div class="activity-bar" style="height: ${height}%"></div>
                    <div class="activity-label">${day.day}</div>
                    <div class="activity-value">${day.minutes}m</div>
                </div>
            `;
        }).join('');
    }

    renderStreak() {
        const currentStreakEl = document.getElementById('currentStreak');
        if (currentStreakEl) {
            currentStreakEl.textContent = this.analytics.currentStreak;
        }
        const streakDescriptionEl = document.querySelector('.streak-description');
        if (streakDescriptionEl) {
            if (this.analytics.currentStreak > 0) {
                streakDescriptionEl.textContent = 'Keep it up!';
            } else {
                streakDescriptionEl.textContent = 'Start your streak today!';
            }
        }
    }

    // Goals UI removed
    // Event handlers
    openAddPlaylistModal() {
        const modal = document.getElementById('addPlaylistModal');
        if (modal) {
            modal.classList.remove('hidden');
        }
    }

    closeAddPlaylistModal() {
        const modal = document.getElementById('addPlaylistModal');
        if (modal) {
            modal.classList.add('hidden');
        }
    }

    async addNewPlaylist() {
        const urlInput = document.getElementById('newPlaylistUrl');
        const confirmBtn = document.getElementById('confirmAddPlaylistBtn');
        const url = urlInput.value.trim();
        
        if (!url) {
            this.showToast('Error', 'Please enter a playlist URL', 'error');
            return;
        }

        // Validate URL
        const youtubeAPI = new YouTubeAPI();
        if (window.CONFIG && window.CONFIG.YOUTUBE_API_KEY) {
            youtubeAPI.init(window.CONFIG.YOUTUBE_API_KEY);
        }

        if (!youtubeAPI.isValidPlaylistURL(url)) {
            this.showToast('Error', 'Please enter a valid YouTube playlist URL', 'error');
            return;
        }

        // Show loading state
        const originalText = confirmBtn.innerHTML;
        confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
        confirmBtn.disabled = true;

        try {
            // Load playlist data
            const playlistData = await youtubeAPI.loadPlaylist(url);
            
            // Add metadata
            playlistData.createdAt = new Date().toISOString();
            playlistData.lastAccessed = new Date().toISOString();
            
            // Save to storage
            this.storage.savePlaylist(playlistData);
            
            // Update local data
            this.playlists.push(playlistData);
            this.calculateAnalytics();
            this.renderDashboard();
            
            this.showToast('Success', `"${playlistData.title}" added successfully!`, 'success');
            this.closeAddPlaylistModal();
            urlInput.value = '';
            
        } catch (error) {
            console.error('Error adding playlist:', error);
            let errorMessage = 'Failed to load playlist. Please try again.';
            
            if (error.message.includes('API request failed')) {
                errorMessage = 'API request failed. Please check your connection.';
            } else if (error.message.includes('not found')) {
                errorMessage = 'Playlist not found or is private.';
            } else if (error.message.includes('quota')) {
                errorMessage = 'API quota exceeded. Please try again later.';
            }
            
            this.showToast('Error', errorMessage, 'error');
        } finally {
            confirmBtn.innerHTML = originalText;
            confirmBtn.disabled = false;
        }
    }

    handleFilterChange(event) {
        document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
        event.target.classList.add('active');
        
        const filter = event.target.dataset.filter;
        this.renderVideos(filter);
    }

    // addGoal removed
    openPlaylist(playlistId) {
        // Save the selected playlist and redirect to main player
        const playlist = this.playlists.find(p => p.id === playlistId);
        if (playlist) {
            // Set as current playlist in storage
            this.storage.setCurrentPlaylist(playlist);
            window.location.href = 'index.html';
        }
    }

    viewPlaylistDetails(playlistId) {
        const playlist = this.playlists.find(p => p.id === playlistId);
        if (playlist) {
            alert(`Playlist: ${playlist.title}\nVideos: ${playlist.videos?.length || 0}\nChannel: ${playlist.channel}`);
        }
    }

    // Utility functions
    formatDuration(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        
        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
    }

    showToast(title, message, type = 'info', duration = 5000) {
        const container = document.getElementById('toastContainer');
        if (!container) return;

        const toast = document.createElement('div');
        toast.className = `toast toast--${type}`;
        
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };

        toast.innerHTML = `
            <div class="toast-icon">
                <i class="${icons[type]}"></i>
            </div>
            <div class="toast-content">
                <div class="toast-title">${title}</div>
                <div class="toast-message">${message}</div>
            </div>
            <button class="toast-close">
                <i class="fas fa-times"></i>
            </button>
        `;

        const closeBtn = toast.querySelector('.toast-close');
        closeBtn.addEventListener('click', () => this.removeToast(toast));

        container.appendChild(toast);

        if (duration > 0) {
            setTimeout(() => this.removeToast(toast), duration);
        }
    }

    removeToast(toast) {
        if (toast && toast.parentNode) {
            toast.classList.add('removing');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }
    }
}

// Initialize dashboard when DOM is loaded
let dashboard;
document.addEventListener('DOMContentLoaded', () => {
    dashboard = new Dashboard();
});
