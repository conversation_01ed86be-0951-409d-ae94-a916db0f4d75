// YouTube API Integration Module
// This module handles all API interactions with YouTube

class YouTubeAPI {
    constructor() {
        this.apiKey = null;
        this.baseURL = 'https://www.googleapis.com/youtube/v3';
        this.initialized = false;
    }

    // Initialize the API with your key
    init(apiKey) {
        this.apiKey = apiKey;
        this.initialized = true;
        console.log('YouTube API initialized');
    }

    // Extract playlist ID from URL
    extractPlaylistId(url) {
        const regex = /[?&]list=([^#\&\?]*)/;
        const match = url.match(regex);
        return match ? match[1] : null;
    }

    // Validate YouTube playlist URL
    isValidPlaylistURL(url) {
        const playlistRegex = /(?:youtube\.com\/playlist\?list=|youtu\.be\/playlist\?list=)([a-zA-Z0-9_-]+)/;
        return playlistRegex.test(url);
    }

    // Fetch playlist information
    async fetchPlaylistInfo(playlistId) {
        if (!this.initialized) {
            throw new Error('API not initialized. Call init() with your API key first.');
        }

        try {
            const response = await fetch(
                `${this.baseURL}/playlists?part=snippet,contentDetails&id=${playlistId}&key=${this.apiKey}`
            );

            if (!response.ok) {
                throw new Error(`API request failed: ${response.status}`);
            }

            const data = await response.json();
            
            if (!data.items || data.items.length === 0) {
                throw new Error('Playlist not found or is private');
            }

            const playlist = data.items[0];
            return {
                id: playlist.id,
                title: playlist.snippet.title,
                description: playlist.snippet.description,
                channel: playlist.snippet.channelTitle,
                videoCount: playlist.contentDetails.itemCount,
                thumbnail: playlist.snippet.thumbnails.medium?.url || playlist.snippet.thumbnails.default?.url,
                publishedAt: playlist.snippet.publishedAt
            };
        } catch (error) {
            console.error('Error fetching playlist info:', error);
            throw error;
        }
    }

    // Fetch all videos in a playlist
    async fetchPlaylistVideos(playlistId) {
        if (!this.initialized) {
            throw new Error('API not initialized. Call init() with your API key first.');
        }

        try {
            let videos = [];
            let nextPageToken = '';
            let position = 1;

            do {
                const response = await fetch(
                    `${this.baseURL}/playlistItems?part=snippet,contentDetails&playlistId=${playlistId}&maxResults=50&pageToken=${nextPageToken}&key=${this.apiKey}`
                );

                if (!response.ok) {
                    throw new Error(`API request failed: ${response.status}`);
                }

                const data = await response.json();
                
                // Get video IDs for duration lookup
                const videoIds = data.items.map(item => item.contentDetails.videoId).join(',');
                
                // Fetch video details including duration
                const videoDetailsResponse = await fetch(
                    `${this.baseURL}/videos?part=contentDetails,snippet&id=${videoIds}&key=${this.apiKey}`
                );

                const videoDetails = await videoDetailsResponse.json();
                const videoDetailsMap = {};
                
                videoDetails.items.forEach(video => {
                    videoDetailsMap[video.id] = video;
                });

                // Process videos
                data.items.forEach(item => {
                    const videoDetail = videoDetailsMap[item.contentDetails.videoId];
                    if (videoDetail) {
                        videos.push({
                            id: item.contentDetails.videoId,
                            title: item.snippet.title,
                            description: item.snippet.description,
                            duration: this.parseDuration(videoDetail.contentDetails.duration),
                            position: position++,
                            thumbnail: item.snippet.thumbnails.medium?.url || item.snippet.thumbnails.default?.url,
                            publishedAt: item.snippet.publishedAt,
                            videoOwnerChannelTitle: item.snippet.videoOwnerChannelTitle,
                            // Progress tracking fields
                            completed: false,
                            currentTime: 0,
                            completionPercentage: 0,
                            lastWatched: null
                        });
                    }
                });

                nextPageToken = data.nextPageToken || '';
            } while (nextPageToken);

            return videos;
        } catch (error) {
            console.error('Error fetching playlist videos:', error);
            throw error;
        }
    }

    // Parse YouTube duration format (PT4M13S) to seconds
    parseDuration(duration) {
        const match = duration.match(/PT(\d+H)?(\d+M)?(\d+S)?/);
        if (!match) return 0;

        const hours = parseInt(match[1]) || 0;
        const minutes = parseInt(match[2]) || 0;
        const seconds = parseInt(match[3]) || 0;

        return hours * 3600 + minutes * 60 + seconds;
    }

    // Load complete playlist data
    async loadPlaylist(url) {
        const playlistId = this.extractPlaylistId(url);
        if (!playlistId) {
            throw new Error('Invalid playlist URL');
        }

        try {
            // Fetch playlist info and videos in parallel
            const [playlistInfo, videos] = await Promise.all([
                this.fetchPlaylistInfo(playlistId),
                this.fetchPlaylistVideos(playlistId)
            ]);

            // Calculate total duration
            const totalDuration = videos.reduce((total, video) => total + video.duration, 0);

            return {
                ...playlistInfo,
                videos,
                totalDuration,
                loadedAt: new Date().toISOString()
            };
        } catch (error) {
            console.error('Error loading playlist:', error);
            throw error;
        }
    }

    // Get video embed URL
    getEmbedURL(videoId, startTime = 0) {
        const params = new URLSearchParams({
            enablejsapi: '1',
            origin: window.location.origin,
            start: Math.floor(startTime)
        });

        return `https://www.youtube.com/embed/${videoId}?${params.toString()}`;
    }

    // Search for playlists (optional feature)
    async searchPlaylists(query, maxResults = 10) {
        if (!this.initialized) {
            throw new Error('API not initialized');
        }

        try {
            const response = await fetch(
                `${this.baseURL}/search?part=snippet&type=playlist&q=${encodeURIComponent(query)}&maxResults=${maxResults}&key=${this.apiKey}`
            );

            if (!response.ok) {
                throw new Error(`Search request failed: ${response.status}`);
            }

            const data = await response.json();
            
            return data.items.map(item => ({
                id: item.id.playlistId,
                title: item.snippet.title,
                description: item.snippet.description,
                channel: item.snippet.channelTitle,
                thumbnail: item.snippet.thumbnails.medium?.url || item.snippet.thumbnails.default?.url,
                publishedAt: item.snippet.publishedAt
            }));
        } catch (error) {
            console.error('Error searching playlists:', error);
            throw error;
        }
    }

    // Check API quota usage (optional)
    async checkQuotaUsage() {
        // This would require additional API endpoints or tracking
        // Implementation depends on your specific needs
        console.log('Quota usage checking not implemented');
    }
}

// Global promise to ensure YouTube IFrame API is ready
let youtubeIframeAPIReadyResolve;
let youtubeIframeAPIReadyReject;
const youtubeIframeAPIReadyPromise = new Promise((resolve, reject) => {
    youtubeIframeAPIReadyResolve = resolve;
    youtubeIframeAPIReadyReject = reject;

    // Add timeout to prevent hanging indefinitely
    setTimeout(() => {
        if (youtubeIframeAPIReadyReject) {
            youtubeIframeAPIReadyReject(new Error('YouTube IFrame API failed to load within timeout'));
        }
    }, 15000);
});

// This function is called by the YouTube IFrame API when it's ready
window.onYouTubeIframeAPIReady = () => {
    console.log('YouTube IFrame API is ready globally.');
    if (youtubeIframeAPIReadyResolve) {
        youtubeIframeAPIReadyResolve();
        youtubeIframeAPIReadyResolve = null;
        youtubeIframeAPIReadyReject = null;
    }
};

// YouTube Player Integration
class YouTubePlayer {
    constructor(containerId) {
        this.containerId = containerId;
        this.player = null;
        this.isReady = false;
        this.currentVideoId = null;
        this.onProgressCallback = null;
        this.onStateChangeCallback = null;
    }

    // Initialize YouTube IFrame API
    async init() {
        // Load YouTube IFrame API script if not already loaded
        if (!document.querySelector('script[src="https://www.youtube.com/iframe_api"]')) {
            const tag = document.createElement('script');
            tag.src = 'https://www.youtube.com/iframe_api';
            tag.async = true;

            // Add error handling for script loading
            const scriptLoadPromise = new Promise((resolve, reject) => {
                tag.onload = resolve;
                tag.onerror = () => reject(new Error('Failed to load YouTube IFrame API script'));
            });

            const firstScriptTag = document.getElementsByTagName('script')[0];
            firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);

            // Wait for script to load
            await scriptLoadPromise;
        }

        // Wait for the global YouTube IFrame API to be ready
        await youtubeIframeAPIReadyPromise;
        
        return new Promise((resolve, reject) => {
            try {
                this.player = new YT.Player(this.containerId, {
                    height: '100%',
                    width: '100%',
                    playerVars: {
                        playsinline: 1,
                        controls: 1,
                        rel: 0,
                        modestbranding: 1
                    },
                    events: {
                        onReady: () => {
                            this.isReady = true;
                            console.log('YouTube player ready');
                            resolve();
                        },
                        onStateChange: (event) => {
                            this.handleStateChange(event);
                        },
                        onError: (event) => {
                            console.error('YouTube player error:', event.data);
                            reject(new Error(`YouTube player error: ${event.data}`));
                        }
                    }
                });
            } catch (error) {
                console.error('Error creating YouTube player instance:', error);
                reject(error);
            }
        });
    }

    // Handle player state changes
    handleStateChange(event) {
        const states = {
            [-1]: 'unstarted',
            [0]: 'ended',
            [1]: 'playing',
            [2]: 'paused',
            [3]: 'buffering',
            [5]: 'cued'
        };

        const state = states[event.data] || 'unknown';
        console.log('Player state changed:', state);

        if (this.onStateChangeCallback) {
            this.onStateChangeCallback(state, event.data);
        }

        // Start progress tracking when playing
        if (event.data === YT.PlayerState.PLAYING) {
            this.startProgressTracking();
        } else {
            this.stopProgressTracking();
        }
    }

    // Load a video
    loadVideo(videoId, startTime = 0) {
        if (!this.isReady) {
            console.error('Player not ready');
            return;
        }

        this.currentVideoId = videoId;
        this.player.loadVideoById({
            videoId: videoId,
            startSeconds: startTime
        });
    }

    // Play/pause controls
    play() {
        if (this.isReady) this.player.playVideo();
    }

    pause() {
        if (this.isReady) this.player.pauseVideo();
    }

    stop() {
        if (this.isReady) this.player.stopVideo();
    }

    // Seek to specific time
    seekTo(seconds) {
        if (this.isReady) {
            this.player.seekTo(seconds, true);
        }
    }

    // Get current time
    getCurrentTime() {
        return this.isReady ? this.player.getCurrentTime() : 0;
    }

    // Get video duration
    getDuration() {
        return this.isReady ? this.player.getDuration() : 0;
    }

    // Get player state
    getPlayerState() {
        return this.isReady ? this.player.getPlayerState() : -1;
    }

    // Progress tracking
    startProgressTracking() {
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
        }

        this.progressInterval = setInterval(() => {
            if (this.onProgressCallback && this.isReady) {
                const currentTime = this.getCurrentTime();
                const duration = this.getDuration();
                const progress = duration > 0 ? (currentTime / duration) * 100 : 0;

                this.onProgressCallback({
                    currentTime,
                    duration,
                    progress,
                    videoId: this.currentVideoId
                });
            }
        }, 1000);
    }

    stopProgressTracking() {
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
            this.progressInterval = null;
        }
    }

    // Set callbacks
    onProgress(callback) {
        this.onProgressCallback = callback;
    }

    onStateChange(callback) {
        this.onStateChangeCallback = callback;
    }

    // Cleanup
    destroy() {
        this.stopProgressTracking();
        if (this.player) {
            this.player.destroy();
        }
    }
}

// Export for use in main application
window.YouTubeAPI = YouTubeAPI;
window.YouTubePlayer = YouTubePlayer;
