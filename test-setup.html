<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Setup - YouTube Playlist Tracker</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        button {
            margin: 10px;
            padding: 10px 15px;
            cursor: pointer;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            background: #f0f0f0;
            border-radius: 5px;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Test Setup for YouTube Playlist Tracker</h1>
    
    <div class="status" id="status">
        Ready to setup test data
    </div>
    
    <div>
        <button onclick="createSamplePlaylist()">Create Sample Playlist</button>
        <button onclick="clearStorage()">Clear Storage</button>
        <button onclick="showCurrentData()">Show Current Data</button>
        <button onclick="openMainApp()">Open Main App</button>
    </div>
    
    <div id="output"></div>

    <script src="config.js"></script>
    <script src="storage.js"></script>
    
    <script>
        const storage = new StorageManager();
        const statusDiv = document.getElementById('status');
        const outputDiv = document.getElementById('output');
        
        function updateStatus(message) {
            statusDiv.textContent = message;
            console.log(message);
        }
        
        function createSamplePlaylist() {
            const samplePlaylist = {
                id: 'PLrAXtmRdnEQy4TyTh9EsEsASMVEzpOpCh',
                title: 'Sample YouTube Playlist',
                description: 'A sample playlist for testing the YouTube tracker',
                channelTitle: 'Test Channel',
                thumbnail: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
                videoCount: 3,
                totalDuration: 600, // 10 minutes
                createdAt: new Date().toISOString(),
                videos: [
                    {
                        id: 'dQw4w9WgXcQ',
                        title: 'Rick Astley - Never Gonna Give You Up (Official Video)',
                        description: 'The official video for Rick Astley',
                        thumbnail: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
                        duration: 212,
                        position: 0,
                        currentTime: 0,
                        completionPercentage: 0,
                        completed: false,
                        lastWatched: null
                    },
                    {
                        id: 'oHg5SJYRHA0',
                        title: 'RickRoll\'d',
                        description: 'Another test video',
                        thumbnail: 'https://i.ytimg.com/vi/oHg5SJYRHA0/maxresdefault.jpg',
                        duration: 188,
                        position: 1,
                        currentTime: 0,
                        completionPercentage: 0,
                        completed: false,
                        lastWatched: null
                    },
                    {
                        id: 'iik25wqIuFo',
                        title: 'Rick Astley - Together Forever (Official Video)',
                        description: 'Third test video',
                        thumbnail: 'https://i.ytimg.com/vi/iik25wqIuFo/maxresdefault.jpg',
                        duration: 200,
                        position: 2,
                        currentTime: 0,
                        completionPercentage: 0,
                        completed: false,
                        lastWatched: null
                    }
                ]
            };
            
            try {
                storage.savePlaylist(samplePlaylist);
                storage.setCurrentPlaylist(samplePlaylist);
                updateStatus('Sample playlist created and set as current!');
                showCurrentData();
            } catch (error) {
                updateStatus(`Error creating playlist: ${error.message}`);
            }
        }
        
        function clearStorage() {
            try {
                localStorage.clear();
                updateStatus('Storage cleared!');
                outputDiv.innerHTML = '';
            } catch (error) {
                updateStatus(`Error clearing storage: ${error.message}`);
            }
        }
        
        function showCurrentData() {
            try {
                const currentPlaylist = storage.getCurrentPlaylist();
                const allPlaylists = storage.getAllPlaylists();
                
                const data = {
                    currentPlaylist: currentPlaylist,
                    allPlaylists: allPlaylists,
                    storageSize: JSON.stringify(localStorage).length
                };
                
                outputDiv.innerHTML = `<h3>Current Storage Data:</h3><pre>${JSON.stringify(data, null, 2)}</pre>`;
                updateStatus('Data displayed below');
            } catch (error) {
                updateStatus(`Error showing data: ${error.message}`);
            }
        }
        
        function openMainApp() {
            window.open('index.html', '_blank');
        }
    </script>
</body>
</html>
