<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube Player Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .player-container {
            width: 100%;
            height: 400px;
            border: 1px solid #ccc;
            margin: 20px 0;
        }
        .controls {
            margin: 20px 0;
        }
        button {
            margin: 5px;
            padding: 10px 15px;
            cursor: pointer;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            background: #f0f0f0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>YouTube Player Test</h1>
    
    <div class="status" id="status">
        Initializing...
    </div>
    
    <div class="player-container" id="player-container">
        <div id="youtube-player"></div>
    </div>
    
    <div class="controls">
        <button onclick="testPlayer()">Test Player</button>
        <button onclick="loadVideo()">Load Test Video</button>
        <button onclick="playVideo()">Play</button>
        <button onclick="pauseVideo()">Pause</button>
    </div>

    <script src="config.js"></script>
    <script src="api.js"></script>
    
    <script>
        let testYouTubePlayer = null;
        const statusDiv = document.getElementById('status');
        
        function updateStatus(message) {
            statusDiv.textContent = message;
            console.log(message);
        }
        
        async function testPlayer() {
            try {
                updateStatus('Creating YouTube player...');
                testYouTubePlayer = new YouTubePlayer('youtube-player');
                
                updateStatus('Initializing player...');
                await testYouTubePlayer.init();
                
                updateStatus('Player initialized successfully!');
                
                // Set up callbacks
                testYouTubePlayer.onStateChange((state) => {
                    updateStatus(`Player state: ${state}`);
                });
                
            } catch (error) {
                updateStatus(`Error: ${error.message}`);
                console.error('Player initialization error:', error);
            }
        }
        
        function loadVideo() {
            if (!testYouTubePlayer) {
                updateStatus('Player not initialized');
                return;
            }
            
            // Load a test video (YouTube's official test video)
            const testVideoId = 'dQw4w9WgXcQ'; // Rick Roll for testing
            testYouTubePlayer.loadVideo(testVideoId);
            updateStatus(`Loading video: ${testVideoId}`);
        }
        
        function playVideo() {
            if (!testYouTubePlayer) {
                updateStatus('Player not initialized');
                return;
            }
            
            testYouTubePlayer.play();
            updateStatus('Playing video');
        }
        
        function pauseVideo() {
            if (!testYouTubePlayer) {
                updateStatus('Player not initialized');
                return;
            }
            
            testYouTubePlayer.pause();
            updateStatus('Pausing video');
        }
        
        // Auto-initialize on page load
        window.addEventListener('load', () => {
            updateStatus('Page loaded. Click "Test Player" to initialize.');
        });
    </script>
</body>
</html>
