<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - IsotopeAI YT Tracker</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div id="app">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <div class="header-left">
                    <h1 class="app-title">
                        <i class="fab fa-youtube"></i>
                        IsotopeAI - YT Tracker Dashboard
                    </h1>
                    
                </div>
                <div class="header-right">
                    <button class="btn btn--outline btn--sm" id="settingsBtn">
                        <i class="fas fa-cog"></i> Settings
                    </button>
                </div>
            </div>
        </header>

        <!-- Dashboard Content -->
        <main class="dashboard-main">
            <!-- Overview Stats -->
            <section class="overview-section">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-list"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value" id="totalPlaylists">0</div>
                            <div class="stat-label">Total Playlists</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-video"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value" id="totalVideos">0</div>
                            <div class="stat-label">Total Videos</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value" id="completedVideos">0</div>
                            <div class="stat-label">Completed</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value" id="totalWatchTime">0h 0m</div>
                            <div class="stat-label">Watch Time</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Main Dashboard Grid -->
            <div class="dashboard-grid">
                <!-- Playlists Section -->
                <section class="dashboard-section">
                    <div class="section-header">
                        <h2>Your Playlists</h2>
                        <button class="btn btn--primary btn--sm" id="addPlaylistBtn">
                            <i class="fas fa-plus"></i> Add Playlist
                        </button>
                    </div>
                    <div class="playlists-grid" id="playlistsGrid">
                        <!-- Playlists will be populated here -->
                    </div>
                </section>

                <!-- Recent Videos Section -->
                <section class="dashboard-section">
                    <div class="section-header">
                        <h2>Recent Videos</h2>
                        <div class="section-filters">
                            <button class="filter-btn active" data-filter="all">All</button>
                            <button class="filter-btn" data-filter="in-progress">In Progress</button>
                            <button class="filter-btn" data-filter="completed">Completed</button>
                        </div>
                    </div>
                    <div class="videos-grid" id="videosGrid">
                        <!-- Videos will be populated here -->
                    </div>
                </section>

                <!-- Analytics Section -->
                <section class="dashboard-section analytics-section">
                    <div class="section-header">
                        <h2>Analytics</h2>
                    </div>
                    <div class="analytics-grid">
                        <!-- Progress Chart -->
                        <div class="analytics-card">
                            <div class="card-header progress-overview-header">
                                <h2>Overall Progress</h2>
                            </div>
                            <div class="card-body">
                                <div class="progress-chart">
                                    <div class="circular-progress-large" id="overallProgressChart">
                                        <div class="progress-circle-large">
                                            <div class="progress-text-large">0%</div>
                                        </div>
                                    </div>
                                    <div class="progress-details">
                                        <div class="progress-item">
                                            <span class="progress-label">Completed</span>
                                            <span class="progress-value" id="completedPercentage">0%</span>
                                        </div>
                                        <div class="progress-item">
                                            <span class="progress-label">In Progress</span>
                                            <span class="progress-value" id="inProgressPercentage">0%</span>
                                        </div>
                                        <div class="progress-item">
                                            <span class="progress-label">Unwatched</span>
                                            <span class="progress-value" id="unwatchedPercentage">0%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Weekly Activity -->
                        <div class="analytics-card">
                            <div class="card-header progress-overview-header">
                                <h2>Weekly Activity</h2>
                            </div>
                            <div class="card-body">
                                <div class="activity-chart" id="weeklyActivity">
                                    <!-- Activity bars will be generated here -->
                                </div>
                            </div>
                        </div>

                        <!-- Learning Streak -->
                        <div class="analytics-card">
                            <div class="card-header progress-overview-header">
                                <h2>Learning Streak</h2>
                            </div>
                            <div class="card-body">
                                <div class="streak-display">
                                    <div class="streak-number" id="currentStreak">0</div>
                                    <div class="streak-label">Days</div>
                                    <div class="streak-description">Keep it up!</div>
                                </div>
                            </div>
                        </div>


                    </div>
                </section>
            </div>
        </main>

        <!-- Footer -->
        <footer class="app-footer">
            <div class="footer-content">
                <span>&copy; 2025 IsotopeAI - YT Tracker. An addon to <a href="https://isotopeai.in" target="_blank">isotopeai.in</a> for enhanced productivity</span>
                <a href="mailto:<EMAIL>" class="footer-contact-link">Contact</a>
            </div>
        </footer>
    </div>

    <!-- Add Playlist Modal -->
    <div class="modal hidden" id="addPlaylistModal">
        <div class="modal-overlay"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3>Add New Playlist</h3>
                <button class="modal-close" id="closeAddPlaylistBtn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">YouTube Playlist URL</label>
                    <input type="url" id="newPlaylistUrl" class="form-control" placeholder="https://www.youtube.com/playlist?list=...">
                    <div class="url-validation" id="newPlaylistValidation"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn--secondary" id="cancelAddPlaylistBtn">Cancel</button>
                <button class="btn btn--primary" id="confirmAddPlaylistBtn">Add Playlist</button>
            </div>
        </div>
    </div>

    <!-- Toast notifications -->
    <div id="toastContainer" class="toast-container"></div>

    <script src="config.js"></script>
    <script src="storage.js"></script>
    <script src="api.js"></script>
    <script src="dashboard.js"></script>
</body>
</html>
