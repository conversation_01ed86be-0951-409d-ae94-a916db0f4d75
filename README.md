# IsotopeAI - YT Tracker

A beautiful, modern web application for tracking your progress through YouTube playlists. An addon to [isotopeai.in](https://isotopeai.in) for enhanced productivity. Perfect for online courses, tutorials, and educational content.

![IsotopeAI - YT Tracker](js_course_progress_dashboard.png)

## ✨ Features

### 🎯 Core Functionality
- **Real YouTube Integration** - Load any public YouTube playlist
- **Progress Tracking** - Automatic progress saving with resume functionality
- **Video Player** - Embedded YouTube player with full controls
- **Smart Navigation** - Easy video switching with keyboard shortcuts
- **Completion Tracking** - Mark videos as complete manually or automatically

### 📊 Analytics & Insights
- **Progress Overview** - Circular progress indicator and detailed stats
- **Video Progress Chart** - Individual video completion visualization
- **Time Tracking** - Total watch time and remaining duration
- **Streak Counter** - Daily learning streak tracking
- **Deadline Management** - Set target completion dates with daily goals

### 🎨 Modern UI/UX
- **Beautiful Design** - Modern, clean interface with smooth animations
- **Dark/Light Mode** - Automatic theme switching with manual override
- **Responsive Design** - Works perfectly on desktop, tablet, and mobile
- **Accessibility** - Full keyboard navigation and screen reader support
- **Toast Notifications** - Elegant feedback for user actions

### 🔧 Advanced Features
- **Data Persistence** - IndexedDB and localStorage support
- **Export/Import** - Backup and restore your progress data
- **Keyboard Shortcuts** - Efficient navigation without mouse
- **Search & Filter** - Find videos quickly with smart filtering
- **Settings Panel** - Customize behavior and preferences

## 🚀 Quick Start

### 1. Setup Files
1. Download all files to a folder on your computer
2. Open `config.js` and replace `'YOUR_API_KEY_HERE'` with your YouTube Data API v3 key

### 2. Get YouTube API Key
1. Go to [Google Cloud Console](https://console.developers.google.com/)
2. Create a new project or select existing one
3. Enable the "YouTube Data API v3"
4. Create credentials (API Key)
5. Copy the API key to `config.js`

### 3. Run the Application
1. Open `index.html` in your web browser
2. Enter a YouTube playlist URL
3. Start tracking your progress!

## 📋 Usage Guide

### Loading a Playlist
1. Copy any public YouTube playlist URL
2. Paste it into the input field
3. Click "Load Playlist" or press Enter
4. Wait for the playlist to load

### Tracking Progress
- **Play/Pause**: Click the play button or press `Space`
- **Seek**: Click on the progress bar or use `←/→` arrow keys
- **Navigate**: Use `↑/↓` arrow keys or click video titles
- **Mark Complete**: Click "Mark Complete" or press `Enter`

### Keyboard Shortcuts
| Key | Action |
|-----|--------|
| `Space` | Play/Pause current video |
| `←` | Seek backward 10 seconds |
| `→` | Seek forward 10 seconds |
| `↑` | Previous video |
| `↓` | Next video |
| `Enter` | Mark current video as complete |
| `Esc` | Close modal/settings |

### Setting Deadlines
1. Open the right sidebar
2. Set a target completion date
3. View daily targets and progress status
4. Stay on track with visual indicators

### Managing Data
- **Export**: Download your progress as JSON file
- **Import**: Restore from previously exported file
- **Auto-save**: Progress saves automatically (can be disabled)
- **Storage**: Choose between IndexedDB or localStorage

## 🛠️ Technical Details

### Architecture
- **Frontend**: Vanilla JavaScript, HTML5, CSS3
- **API**: YouTube Data API v3
- **Storage**: IndexedDB with localStorage fallback
- **Player**: YouTube IFrame API

### Browser Support
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

### File Structure
```
├── index.html          # Main HTML file
├── app.js             # Core application logic
├── api.js             # YouTube API integration
├── style.css          # Comprehensive styling
├── config.js          # Configuration and API key
└── README.md          # This file
```

## 🎨 Customization

### Themes
The app supports three theme modes:
- **Light**: Clean, bright interface
- **Dark**: Easy on the eyes for long sessions
- **Auto**: Follows system preference

### Settings
Customize the app behavior:
- **Auto-save**: Automatically save progress
- **Auto-complete**: Mark videos complete at 95%
- **Notifications**: Show toast notifications
- **Storage**: Choose storage method

## 🔒 Privacy & Security

- **No Data Collection**: All data stays on your device
- **Local Storage**: Progress saved locally in your browser
- **API Security**: YouTube API key only used for playlist data
- **No Tracking**: No analytics or user tracking
- **IsotopeAI Integration**: Seamlessly works with the IsotopeAI productivity ecosystem

## 🐛 Troubleshooting

### Common Issues

**"API not initialized" error**
- Check that your API key is correctly set in `config.js`
- Ensure the YouTube Data API v3 is enabled in Google Cloud Console

**Playlist won't load**
- Verify the playlist is public (not private or unlisted)
- Check your internet connection
- Try a different playlist URL

**Videos won't play**
- Some videos may be restricted by the creator
- Try refreshing the page
- Check if the video is available in your region

**Progress not saving**
- Check browser storage permissions
- Try switching storage method in settings
- Clear browser cache and reload

### Demo Mode
If no API key is provided, the app runs in demo mode with sample data. This is perfect for testing the interface and features.

## 🤝 Contributing

This is a complete, production-ready application. Feel free to:
- Report bugs or issues
- Suggest new features
- Submit improvements
- Share your experience

## 📄 License

This project is open source and available under the MIT License.

## 🙏 Acknowledgments

- YouTube for the comprehensive API
- Font Awesome for beautiful icons
- Modern CSS design system principles
- Accessibility guidelines and best practices
- [IsotopeAI](https://isotopeai.in) for the productivity ecosystem

---

**Happy Learning with IsotopeAI!** 🎓

Track your progress, stay motivated, and complete those playlists with enhanced productivity! 🚀